<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: Arial, sans-serif;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .logo {
      font-size: 18px;
      font-weight: bold;
      color: #2196F3;
      margin-bottom: 5px;
    }
    
    .subtitle {
      font-size: 12px;
      color: #666;
    }
    
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      text-align: center;
      font-size: 14px;
    }
    
    .status.ready {
      background-color: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }
    
    .status.working {
      background-color: #fff3e0;
      color: #f57c00;
      border: 1px solid #ffcc02;
    }
    
    .status.error {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }

    .status.success {
      background-color: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
      font-weight: bold;
    }

    .status.info {
      background-color: #e3f2fd;
      color: #1565c0;
      border: 1px solid #bbdefb;
    }
    
    .config {
      margin-bottom: 15px;
    }
    
    .config-item {
      margin-bottom: 10px;
    }
    
    .config-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 3px;
    }
    
    .config-value {
      font-size: 14px;
      color: #333;
      background-color: #f5f5f5;
      padding: 5px 8px;
      border-radius: 3px;
      word-break: break-all;
    }
    
    .buttons {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      flex: 1;
      padding: 8px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: background-color 0.2s;
    }
    
    .btn-primary {
      background-color: #2196F3;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #1976D2;
    }
    
    .btn-secondary {
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #ddd;
    }
    
    .btn-secondary:hover {
      background-color: #e0e0e0;
    }
    
    .logs {
      margin-top: 15px;
      max-height: 100px;
      overflow-y: auto;
      font-size: 11px;
      background-color: #f9f9f9;
      padding: 8px;
      border-radius: 3px;
      border: 1px solid #e0e0e0;
    }
    
    .log-entry {
      margin-bottom: 3px;
      color: #666;
      font-size: 11px;
      line-height: 1.3;
    }
    
    .toggle-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      padding: 8px;
      background-color: #f9f9f9;
      border-radius: 5px;
    }
    
    .toggle-label {
      font-size: 14px;
      color: #333;
    }
    
    .toggle-switch {
      position: relative;
      width: 40px;
      height: 20px;
      background-color: #ccc;
      border-radius: 20px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .toggle-switch.active {
      background-color: #2196F3;
    }
    
    .toggle-switch::after {
      content: '';
      position: absolute;
      width: 16px;
      height: 16px;
      background-color: white;
      border-radius: 50%;
      top: 2px;
      left: 2px;
      transition: transform 0.2s;
    }
    
    .toggle-switch.active::after {
      transform: translateX(20px);
    }

    #autoLoginToggle {
      display: none;
    }

    .email-container {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .email-container .config-value {
      flex: 1;
      margin: 0;
    }

    .refresh-btn {
      background-color: #f5f5f5;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 4px 8px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s;
      min-width: 32px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .refresh-btn:hover {
      background-color: #e0e0e0;
      border-color: #ccc;
    }

    .refresh-btn:active {
      background-color: #d0d0d0;
      transform: scale(0.95);
    }

    .refresh-btn.loading {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">Augment Code Auto Login</div>
    <div class="subtitle">自动登录助手</div>
  </div>
  
  <div id="status" class="status ready">
    ✅ 准备就绪
  </div>
  
  <div class="toggle-container">
    <span class="toggle-label">自动登录</span>
    <input type="checkbox" id="autoLoginToggle" checked>
    <div id="toggleSwitch" class="toggle-switch active"></div>
  </div>
  
  <div class="config">
    <div class="config-item">
      <div class="config-label">目标网站:</div>
      <div class="config-value">https://app.augmentcode.com/</div>
    </div>
    <div class="config-item">
      <div class="config-label">注册邮箱:</div>
      <div class="email-container">
        <div class="config-value" id="emailValue">正在生成...</div>
        <button id="refreshEmailBtn" class="refresh-btn" title="生成新的随机邮箱">🔄</button>
      </div>
    </div>
  </div>
  
  <div class="buttons">
    <button id="loginBtn" class="btn btn-primary">开始登录</button>
    <button id="settingsBtn" class="btn btn-secondary">设置</button>
  </div>
  
  <div id="logs" class="logs">
    <!-- 日志将在这里显示 -->
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
